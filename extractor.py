from pytube import YouTube
from youtube_transcript_api import YouTubeTranscriptApi
import re

def get_video_id(url):
    """
    Extrai o ID do vídeo da URL do YouTube.
    """
    # Padrões comuns de URL do YouTube
    patterns = [
        r'(?:v=|\/)([0-9A-Za-z_-]{11}).*',
        r'(?:youtu\.be\/)([0-9A-Za-z_-]{11})',
        r'(?:embed\/)([0-9A-Za-z_-]{11})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    raise ValueError("URL do YouTube inválida")

def get_transcript(url):
    """
    Obtém a transcrição do vídeo do YouTube.
    
    Args:
        url (str): URL do vídeo
        
    Returns:
        list: Lista de dicionários com timestamps e texto
    """
    video_id = get_video_id(url)
    try:
        transcript = YouTubeTranscriptApi.get_transcript(
            video_id, 
            languages=['pt', 'en']
        )
        return transcript
    except Exception as e:
        raise Exception(f"Erro ao obter transcrição: {str(e)}")

def download_video(url, output_path):
    """
    Baixa o vídeo do YouTube.
    
    Args:
        url (str): URL do vídeo
        output_path (str): Caminho para salvar o vídeo
        
    Returns:
        str: Caminho do arquivo baixado
    """
    try:
        yt = YouTube(url)
        # Pega a melhor resolução disponível
        stream = yt.streams.get_highest_resolution()
        # Faz o download
        stream.download(filename=output_path)
        return output_path
    except Exception as e:
        raise Exception(f"Erro ao baixar vídeo: {str(e)}") 