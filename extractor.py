from pytube import YouTube
from youtube_transcript_api import YouTubeTranscriptApi
import re

def get_video_id(url):
    """
    Extrai o ID do vídeo da URL do YouTube.
    """
    # Padrões comuns de URL do YouTube
    patterns = [
        r'(?:v=|\/)([0-9A-Za-z_-]{11}).*',
        r'(?:youtu\.be\/)([0-9A-Za-z_-]{11})',
        r'(?:embed\/)([0-9A-Za-z_-]{11})'
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    raise ValueError("URL do YouTube inválida")

def get_transcript(url):
    """
    Obtém a transcrição do vídeo do YouTube.

    Args:
        url (str): URL do vídeo

    Returns:
        list: Lista de dicionários com timestamps e texto
    """
    video_id = get_video_id(url)

    # Primeiro tenta obter transcrição em português (que sabemos que está disponível)
    try:
        print("Tentando obter transcrição em português...")
        transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['pt'])
        print(f"Transcrição obtida em português com {len(transcript)} segmentos")
        return transcript
    except Exception as e:
        print(f"Falha ao obter transcrição em português: {str(e)}")

    # Se falhar, tenta inglês
    try:
        print("Tentando obter transcrição em inglês...")
        transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['en'])
        print(f"Transcrição obtida em inglês com {len(transcript)} segmentos")
        return transcript
    except Exception as e:
        print(f"Falha ao obter transcrição em inglês: {str(e)}")

    # Se nenhum idioma específico funcionou, tenta sem especificar idioma
    try:
        print("Tentando obter qualquer transcrição disponível...")
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        for transcript_info in transcript_list:
            try:
                transcript = transcript_info.fetch()
                print(f"Transcrição obtida em {transcript_info.language_code} com {len(transcript)} segmentos")
                return transcript
            except:
                continue
    except Exception as e:
        print(f"Erro ao listar transcrições: {str(e)}")

    # Se chegou até aqui, não conseguiu obter nenhuma transcrição
    raise Exception("Não foi possível obter transcrição para este vídeo. O vídeo pode não ter legendas disponíveis.")



def download_video(url, output_path):
    """
    Baixa o vídeo do YouTube.

    Args:
        url (str): URL do vídeo
        output_path (str): Caminho para salvar o vídeo

    Returns:
        str: Caminho do arquivo baixado
    """
    import os

    try:
        print(f"Tentando baixar vídeo: {url}")

        # Tenta diferentes abordagens para contornar problemas da pytube
        yt = YouTube(url)

        # Tenta primeiro com resolução mais baixa (mais estável)
        stream = yt.streams.filter(progressive=True, file_extension='mp4').first()
        if not stream:
            # Se não encontrar progressivo, tenta adaptativo
            stream = yt.streams.filter(adaptive=True, file_extension='mp4').first()
        if not stream:
            # Como último recurso, pega qualquer stream disponível
            stream = yt.streams.first()

        if stream:
            print(f"Stream encontrado: {stream.resolution} - {stream.mime_type}")
            # Cria o diretório se não existir
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            # Faz o download
            stream.download(filename=output_path)
            print(f"✅ Vídeo baixado com sucesso: {output_path}")
            return output_path
        else:
            raise Exception("Nenhum stream de vídeo disponível")

    except Exception as e:
        raise Exception(f"Erro ao baixar vídeo: {str(e)}")