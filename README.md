# Cut Scene - Criador de Conteúdo Transformativo

Uma ferramenta avançada para criar conteúdo transformativo original a partir de vídeos do YouTube, garantindo conformidade com as novas políticas de monetização através de valor agregado significativo.

## Funcionalidades

### Análise e Extração
- Extração automática de transcrição de vídeos do YouTube
- Análise de conteúdo usando IA para identificar momentos importantes
- Geração de scripts editoriais originais para cada segmento

### Enriquecimento Editorial
- **Narração própria**: TTS em português para comentários originais
- **Elementos visuais únicos**: Legendas, introdução e conclusão personalizadas
- **Scripts de comentário**: Análise editorial original com pelo menos 100 palavras por segmento
- **Estrutura narrativa própria**: Introdução e conclusão originais

### Otimização Viral (NOVO)
- **Títulos Expert**: Estratégias de MrBeast, <PERSON>, <PERSON><PERSON> e <PERSON>
- **An<PERSON>lise Psicológica**: Gatilhos emocionais e elementos virais
- **Copywriting Viral**: Descrições otimizadas para engajamento
- **Sugestões de Thumbnail**: Baseadas em análise emocional

### Conformidade com Políticas
- ✅ Transformação significativa do conteúdo original
- ✅ Valor educacional e analítico agregado
- ✅ Narração e comentários próprios
- ✅ Elementos visuais únicos
- ✅ Análise crítica e insights exclusivos
- ✅ Otimização baseada em creators de sucesso

## Pré-requisitos

1. Python 3.8 ou superior
2. FFmpeg instalado e disponível no PATH do sistema
3. Dependências Python listadas em `requirements.txt`

## Instalação

1. Clone o repositório:
```bash
git clone https://github.com/seu-usuario/cut-scene.git
cd cut-scene
```

2. Instale as dependências:
```bash
pip install -r requirements.txt
```

3. Certifique-se de que o FFmpeg está instalado:
```bash
ffmpeg -version
```

## Uso

1. Inicie o aplicativo:
```bash
streamlit run app.py
```

2. Abra o navegador no endereço indicado (geralmente http://localhost:8501)

3. Cole a URL do vídeo do YouTube que deseja analisar

4. Clique em "Analisar e Gerar Cortes"

5. Aguarde o processamento:
   - Extração da transcrição
   - Análise editorial com IA
   - Download do vídeo
   - Geração dos cortes brutos
   - **Enriquecimento editorial** (narração + elementos visuais)
   - Criação do relatório editorial

6. Acesse os resultados:
   - Análise transformativa
   - Scripts editoriais originais
   - Vídeos enriquecidos com narração própria
   - Cortes brutos (sem enriquecimento)
   - Relatório de conformidade com políticas
   - Títulos e descrições otimizados

## Estrutura do Projeto

```
cut-scene/
│
├── app.py                  # Interface Streamlit
├── router_client.py        # Cliente OpenRouter
├── extractor.py           # Funções de extração
├── cutter.py              # Funções de corte
├── enhancer.py            # Enriquecimento editorial
├── youtube_optimizer.py   # Otimização viral (NOVO)
├── requirements.txt       # Dependências
└── outputs/              # Diretório de saída
    ├── segments/         # Cortes brutos
    ├── enhanced/         # Vídeos enriquecidos (finais)
    └── report.md        # Relatório editorial completo
```

## Contribuição

Sinta-se à vontade para contribuir com o projeto:

1. Faça um Fork
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes. 