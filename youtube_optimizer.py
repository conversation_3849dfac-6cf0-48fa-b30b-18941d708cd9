from router_client import make_api_call

def optimize_titles_with_expert_strategies(transcript, video_summary, segments):
    """
    Otimiza títulos usando estratégias dos maiores criadores do YouTube.
    Baseado em Mr<PERSON>, <PERSON>, <PERSON><PERSON> e <PERSON>.
    """

    # Prepara contexto do vídeo
    context = f"""
    RESUMO DO VÍDEO: {video_summary}

    MOMENTOS-CHAVE:
    {chr(10).join([f"- {seg['start']:.1f}s: {seg['motivo']}" for seg in segments])}

    TRANSCRIÇÃO RESUMIDA:
    {transcript[:1000]}...
    """

    expert_prompt = f"""Você é um especialista em estratégia de crescimento do YouTube, treinado em psicologia da atenção, gatilhos comportamentais e filosofias de conteúdo de Mr<PERSON>, <PERSON>, <PERSON><PERSON> e <PERSON>.

Sua tarefa é otimizar títulos de vídeo do YouTube para maximizar CTR (taxa de cliques), curiosidade e atração emocional — sem ser enganoso.

CONTEXTO DO VÍDEO:
{context}

Considere o seguinte ao elaborar os títulos:
• Lacuna de curiosidade (mas sem clickbait)
• Intensidade emocional (surpresa, espanto, medo, alegria, raiva, etc.)
• Especificidade (usar números, prazos, contraste, apostas)
• Interrupção de padrões e exclusividade (destaque-se do que já está na plataforma)
• Fórmulas virais usadas pelos principais criadores:
  - "Eu fiz X para você não ter que fazer"
  - "Isso mudou minha vida"
  - "Como [X] em [Y] dias"
  - "O que acontece quando..."
  - "Ninguém te conta sobre..."
  - "A verdade por trás de..."

FILOSOFIAS DOS EXPERTS:
• MrBeast: Faça o espectador dizer "EU PRECISO saber o que acontece"
• Paddy Galloway: clareza, curiosidade e relevância emocional
• Derral Eves: foco em valor e transformação
• Ryan Trahan: ganchos narrativos e identificação pessoal

Crie 5 títulos altamente clicáveis (máx. 60 caracteres cada) que:
1. Falem diretamente com a mente subconsciente
2. Desencadeiem vontade irresistível de clicar
3. Superariam títulos médios em testes A/B
4. Sejam honestos e entreguem o que prometem

Responda APENAS com os 5 títulos, um por linha, sem numeração ou explicação."""

    try:
        content = make_api_call(expert_prompt, temperature=0.8, max_tokens=300)
        titles = content.strip().split('\n')
        # Remove linhas vazias e limita a 60 caracteres
        optimized_titles = []
        for title in titles:
            title = title.strip()
            if title and len(title) <= 60:
                optimized_titles.append(title)

        return optimized_titles[:5]  # Garante máximo 5 títulos

    except Exception as e:
        print(f"Erro ao otimizar títulos: {str(e)}")
        # Fallback com títulos básicos
        return [
            "Você NÃO VAI ACREDITAR no que descobri...",
            "Isso mudou TUDO que eu sabia sobre...",
            "A verdade que NINGUÉM te conta",
            "Como [resultado] em apenas [tempo]",
            "Eu fiz isso para você não precisar"
        ]

def generate_viral_descriptions(video_summary, segments, optimized_titles):
    """
    Gera descrições otimizadas usando técnicas de copywriting viral.
    """

    description_prompt = f"""Como especialista em copywriting para YouTube, crie uma descrição viral baseada em:

RESUMO: {video_summary}
TÍTULOS OTIMIZADOS: {', '.join(optimized_titles)}
SEGMENTOS-CHAVE: {len(segments)} momentos impactantes

A descrição deve:
• Começar com GANCHO FORTE (primeiras 2 linhas)
• Usar storytelling envolvente
• Incluir call-to-actions estratégicos
• Ter palavras-chave naturalmente integradas
• Criar senso de urgência/escassez
• Incluir social proof quando possível
• Terminar com CTA poderoso

Estrutura:
1. GANCHO (2 linhas)
2. CONTEXTO INTRIGANTE (3-4 linhas)
3. PROMESSA DE VALOR (2 linhas)
4. TIMESTAMPS dos momentos-chave
5. CTA FINAL

Máximo 500 palavras. Tom: conversacional, energético, autêntico."""

    try:
        content = make_api_call(description_prompt, temperature=0.7, max_tokens=800)
        return content.strip()

    except Exception as e:
        print(f"Erro ao gerar descrição: {str(e)}")
        return f"Descobri algo incrível analisando este conteúdo...\n\n{video_summary}\n\n⏰ TIMESTAMPS:\n" + \
               "\n".join([f"{seg['start']:.0f}:{seg['start']%60:02.0f} - {seg['motivo']}" for seg in segments])

def analyze_emotional_triggers(segments):
    """
    Analisa os gatilhos emocionais presentes nos segmentos para orientar a estratégia.
    """

    triggers_prompt = f"""Analise os seguintes segmentos e identifique os GATILHOS EMOCIONAIS mais fortes:

SEGMENTOS:
{chr(10).join([f"• {seg['motivo']}" for seg in segments])}

Identifique:
1. EMOÇÕES PRIMÁRIAS (medo, alegria, raiva, surpresa, nojo, tristeza)
2. GATILHOS PSICOLÓGICOS (escassez, autoridade, prova social, reciprocidade)
3. ELEMENTOS VIRAIS (controvérsia, transformação, segredo, descoberta)
4. PONTOS DE DOR/PRAZER do público
5. HOOKS NARRATIVOS mais poderosos

Responda em formato JSON:
{{
    "emocoes_primarias": ["emoção1", "emoção2"],
    "gatilhos_psicologicos": ["gatilho1", "gatilho2"],
    "elementos_virais": ["elemento1", "elemento2"],
    "hooks_principais": ["hook1", "hook2"],
    "estrategia_recomendada": "string"
}}"""

    try:
        content = make_api_call(triggers_prompt, temperature=0.6)
        result = eval(content)
        return result

    except Exception as e:
        print(f"Erro ao analisar gatilhos: {str(e)}")
        return {
            "emocoes_primarias": ["curiosidade", "surpresa"],
            "gatilhos_psicologicos": ["escassez", "prova_social"],
            "elementos_virais": ["descoberta", "transformação"],
            "hooks_principais": ["revelação", "contraste"],
            "estrategia_recomendada": "Focar na curiosidade e transformação pessoal"
        }

def generate_thumbnail_suggestions(video_summary, emotional_analysis):
    """
    Gera sugestões de thumbnail baseadas na análise emocional.
    """

    thumbnail_prompt = f"""Como especialista em thumbnails virais do YouTube, sugira elementos visuais baseado em:

RESUMO: {video_summary}
EMOÇÕES IDENTIFICADAS: {emotional_analysis.get('emocoes_primarias', [])}
GATILHOS: {emotional_analysis.get('gatilhos_psicologicos', [])}

Crie 3 conceitos de thumbnail que:
• Usem expressões faciais impactantes
• Incluam elementos de contraste visual
• Tenham texto mínimo mas poderoso
• Criem curiosidade instantânea
• Se destaquem no feed do YouTube

Para cada conceito, descreva:
1. EXPRESSÃO FACIAL/POSE
2. CORES DOMINANTES
3. TEXTO (se houver)
4. ELEMENTOS VISUAIS
5. EMOÇÃO TRANSMITIDA

Formato: Lista numerada, máx. 50 palavras por conceito."""

    try:
        content = make_api_call(thumbnail_prompt, temperature=0.7, max_tokens=600)
        return content.strip()

    except Exception as e:
        print(f"Erro ao gerar sugestões de thumbnail: {str(e)}")
        return "1. Expressão de choque com cores vibrantes\n2. Contraste antes/depois\n3. Elementos de mistério com interrogação"