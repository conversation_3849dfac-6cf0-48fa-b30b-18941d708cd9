from openai import OpenAI
import json

OPENROUTER_API_KEY = "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"

# Configuração do cliente OpenAI para OpenRouter
client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key=OPENROUTER_API_KEY,
)

def make_api_call(prompt, model="mistralai/mistral-small-3:free", temperature=0.7, max_tokens=None):
    """
    Função genérica para fazer chamadas à API do OpenRouter usando OpenAI client.
    """
    # MODO DEMONSTRAÇÃO: Usando respostas simuladas para teste do sistema
    print("🤖 Usando modo demonstração - gerando resposta simulada...")

    if "analise a transcrição" in prompt.lower() or "segments" in prompt.lower():
        return generate_demo_analysis()
    elif "títulos" in prompt.lower() or "titles" in prompt.lower():
        return generate_demo_titles()
    elif "descrição" in prompt.lower() or "description" in prompt.lower():
        return generate_demo_description()
    elif "gatilhos emocionais" in prompt.lower() or "emotional" in prompt.lower():
        return generate_demo_emotional_analysis()
    elif "thumbnail" in prompt.lower():
        return generate_demo_thumbnails()
    else:
        return "Esta é uma resposta simulada para demonstração do sistema Cut Scene."

    # CÓDIGO ORIGINAL (comentado para demonstração):
    # messages = [{"role": "user", "content": prompt}]
    #
    # # Para o modelo NVIDIA Nemotron, adiciona instruções de reasoning no system prompt
    # if "nemotron" in model.lower():
    #     messages = [
    #         {
    #             "role": "system",
    #             "content": "You are a helpful assistant. Think step by step and provide detailed reasoning for your responses. Use <thinking> tags to show your reasoning process when helpful."
    #         },
    #         {"role": "user", "content": prompt}
    #     ]
    #
    # kwargs = {
    #     "model": model,
    #     "messages": messages,
    #     "temperature": temperature,
    #     "extra_headers": {
    #         "HTTP-Referer": "https://cut-scene-app.local",
    #         "X-Title": "Cut Scene - Video Content Optimizer",
    #     }
    # }
    #
    # if max_tokens:
    #     kwargs["max_tokens"] = max_tokens
    #
    # completion = client.chat.completions.create(**kwargs)
    # return completion.choices[0].message.content

def generate_demo_analysis():
    """Gera análise simulada para demonstração"""
    return """{
    "segments": [
        {
            "start": 0.0,
            "end": 15.0,
            "motivo": "Introdução cativante que estabelece o gancho principal do vídeo",
            "script": "Este segmento inicial é fundamental pois estabelece o tom e a promessa de valor do conteúdo. A forma como o criador se apresenta e introduz o tópico demonstra técnicas avançadas de storytelling que mantêm a audiência engajada desde os primeiros segundos. É interessante notar como ele usa elementos de curiosidade e antecipação para garantir que os espectadores continuem assistindo. Esta abordagem é essencial no YouTube atual, onde os primeiros 15 segundos determinam se o vídeo será bem-sucedido ou não."
        },
        {
            "start": 15.0,
            "end": 45.0,
            "motivo": "Desenvolvimento do argumento principal com exemplos práticos",
            "script": "Aqui vemos uma demonstração magistral de como transformar teoria em prática. O criador não apenas explica conceitos, mas os ilustra com exemplos concretos que ressoam com a experiência do público. Esta técnica de ensino é particularmente eficaz porque permite que os espectadores visualizem como aplicar as informações em suas próprias vidas. A progressão lógica dos pontos e a clareza na explicação mostram um domínio profundo do assunto e uma compreensão aguçada de como comunicar ideias complexas de forma acessível."
        },
        {
            "start": 45.0,
            "end": 75.0,
            "motivo": "Momento de transformação e revelação surpreendente",
            "script": "Este é o ponto de virada do vídeo, onde a verdadeira revelação acontece. A forma como o criador constrói a tensão até este momento e depois entrega uma informação que subverte as expectativas é um exemplo perfeito de storytelling eficaz. Esta técnica de 'plot twist' mantém a audiência completamente engajada e cria um momento memorável que será compartilhado e comentado. É precisamente este tipo de conteúdo que gera discussões e aumenta o engajamento orgânico."
        },
        {
            "start": 75.0,
            "end": 96.0,
            "motivo": "Conclusão impactante com call-to-action estratégico",
            "script": "O encerramento demonstra como finalizar um vídeo de forma que maximize tanto a satisfação do espectador quanto as métricas de engajamento. A recapitulação dos pontos principais reforça a mensagem central, enquanto o call-to-action é posicionado de forma natural e persuasiva. Esta abordagem garante que os espectadores saiam do vídeo com uma sensação de completude e valor agregado, aumentando a probabilidade de interação e fidelização do público."
        }
    ],
    "summary": "Este vídeo representa um exemplo excepcional de conteúdo educacional transformativo que combina storytelling envolvente com informações práticas valiosas. A estrutura narrativa demonstra técnicas avançadas de retenção de audiência, desde o gancho inicial até a conclusão satisfatória. O criador mostra domínio tanto do assunto quanto das técnicas de comunicação digital, resultando em um conteúdo que não apenas informa, mas também entretém e inspira ação.",
    "titles": [
        "Como Transformar Sua Abordagem em Apenas 5 Minutos",
        "A Técnica Secreta Que Mudou Tudo Para Mim",
        "Você Não Vai Acreditar No Que Descobri Hoje"
    ],
    "description": "Um guia completo e transformativo que revela estratégias práticas para alcançar resultados extraordinários. Este vídeo combina insights profundos com aplicações práticas que você pode implementar imediatamente.",
    "intro_text": "Bem-vindos a uma análise editorial exclusiva deste conteúdo transformativo. Nosso objetivo é extrair e amplificar os insights mais valiosos, oferecendo uma perspectiva única e comentários especializados que agregam valor significativo ao material original.",
    "outro_text": "Esta análise demonstra como conteúdo de qualidade pode ser transformado em material educacional ainda mais rico através de comentários especializados e insights exclusivos. Continuamos comprometidos em oferecer análises que respeitam o trabalho original enquanto agregam valor substancial através de nossa perspectiva editorial única."
}"""

def generate_demo_titles():
    """Gera títulos simulados para demonstração"""
    return """Isso VAI MUDAR sua vida para sempre
Como eu descobri o SEGREDO que ninguém conta
A VERDADE por trás do que você sempre acreditou
Você NÃO VAI ACREDITAR no que aconteceu
O método que TRANSFORMOU minha realidade"""

def generate_demo_description():
    """Gera descrição simulada para demonstração"""
    return """🔥 PREPARE-SE para uma revelação que vai TRANSFORMAR sua perspectiva!

Neste vídeo, mergulhamos fundo em descobertas que a maioria das pessoas nunca teve acesso. Depois de meses de pesquisa e análise, finalmente posso compartilhar insights que mudaram completamente minha abordagem.

✨ O QUE VOCÊ VAI DESCOBRIR:
• A estratégia secreta que poucos conhecem
• Como aplicar essas técnicas na prática
• Resultados reais e transformações impressionantes
• Dicas exclusivas que não encontrará em lugar nenhum

⏰ TIMESTAMPS:
00:00 - Introdução: O que mudou tudo
00:15 - A descoberta que ninguém esperava
00:45 - Exemplos práticos e resultados reais
01:15 - Como você pode aplicar isso hoje

💡 Esta análise editorial oferece perspectivas únicas e comentários especializados que agregam valor significativo ao conteúdo original.

👍 Se este conteúdo agregou valor para você, deixe seu LIKE e COMPARTILHE com quem precisa ver isso!

🔔 ATIVE as notificações para não perder nenhuma análise exclusiva!

#Transformação #Descoberta #Análise #ConteúdoExclusivo"""

def generate_demo_emotional_analysis():
    """Gera análise emocional simulada para demonstração"""
    return """{
    "emocoes_primarias": ["curiosidade", "surpresa", "esperança", "admiração"],
    "gatilhos_psicologicos": ["escassez", "prova_social", "autoridade", "reciprocidade"],
    "elementos_virais": ["revelação", "transformação", "segredo", "descoberta"],
    "hooks_principais": ["mudança de vida", "segredo revelado", "transformação pessoal"],
    "estrategia_recomendada": "Focar na curiosidade e transformação pessoal, usando elementos de revelação e descoberta para maximizar o engajamento. Enfatizar a exclusividade e o valor prático das informações."
}"""

def generate_demo_thumbnails():
    """Gera sugestões de thumbnail simuladas para demonstração"""
    return """1. **EXPRESSÃO DE CHOQUE**: Rosto com olhos arregalados, boca aberta, cores vibrantes (vermelho/amarelo), texto "NÃO ACREDITO!", fundo com elementos de surpresa e descoberta.

2. **ANTES E DEPOIS**: Split screen mostrando contraste dramático, expressão de satisfação, cores contrastantes (azul/laranja), texto "TRANSFORMAÇÃO", elementos visuais de progresso.

3. **MISTÉRIO E REVELAÇÃO**: Expressão pensativa com dedo no queixo, cores escuras com destaque dourado, texto "O SEGREDO", elementos de interrogação e descoberta, iluminação dramática."""

def analyze_transcript(transcript):
    """
    Analisa a transcrição usando OpenRouter para identificar segmentos importantes.

    Args:
        transcript (list): Lista de dicionários com timestamps e texto

    Returns:
        tuple: (segments, summary, titles, description)
    """
    # Prepara o texto da transcrição
    full_text = "\n".join([f"{t['start']:.1f}: {t['text']}" for t in transcript])

    # Prompt para análise
    prompt = f"""Analise a transcrição abaixo e crie conteúdo editorial original que adiciona valor significativo:

1. 3-5 segmentos mais importantes (com timestamp início e fim)
2. Para cada segmento, crie um SCRIPT DE COMENTÁRIO original que:
   - Explica o contexto e importância
   - Adiciona insights exclusivos
   - Oferece análise crítica ou perspectiva única
   - Tem pelo menos 100 palavras para garantir substância
3. Um resumo geral transformativo
4. 3 sugestões de títulos otimizados
5. Uma descrição otimizada
6. Texto de introdução original (para o primeiro vídeo)
7. Texto de conclusão original (para o último vídeo)

Transcrição:
{full_text}

Responda em formato JSON:
{{
    "segments": [
        {{
            "start": float,
            "end": float,
            "motivo": "string",
            "script": "string - comentário editorial original de pelo menos 100 palavras"
        }},
        ...
    ],
    "summary": "string - análise transformativa original",
    "titles": ["string", ...],
    "description": "string - descrição com valor agregado",
    "intro_text": "string - introdução original para contextualizar",
    "outro_text": "string - conclusão original com insights finais"
}}"""

    # Faz a chamada à API
    content = make_api_call(prompt, temperature=0.7)

    # Processa a resposta
    try:
        # Tenta fazer parse do JSON
        try:
            result = json.loads(content)
        except json.JSONDecodeError:
            # Se falhar, tenta usar eval como fallback
            result = eval(content)

        return (
            result["segments"],
            result["summary"],
            result["titles"],
            result["description"],
            result.get("intro_text", ""),
            result.get("outro_text", "")
        )
    except Exception as e:
        raise Exception(f"Erro ao processar resposta do LLM: {str(e)}")