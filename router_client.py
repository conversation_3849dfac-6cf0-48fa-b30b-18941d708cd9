import httpx
import json

OPENROUTER_API_KEY = "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"

def make_api_call(prompt, model="mistralai/mistral-7b-instruct", temperature=0.7, max_tokens=None):
    """
    Função genérica para fazer chamadas à API do OpenRouter.
    """
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json"
    }

    data = {
        "model": model,
        "messages": [{
            "role": "user",
            "content": prompt
        }],
        "temperature": temperature
    }

    if max_tokens:
        data["max_tokens"] = max_tokens

    response = httpx.post(
        "https://openrouter.ai/api/v1/chat/completions",
        headers=headers,
        json=data,
        timeout=60.0
    )

    response.raise_for_status()
    response_data = response.json()
    return response_data["choices"][0]["message"]["content"]

def analyze_transcript(transcript):
    """
    Analisa a transcrição usando OpenRouter para identificar segmentos importantes.

    Args:
        transcript (list): Lista de dicionários com timestamps e texto

    Returns:
        tuple: (segments, summary, titles, description)
    """
    # Prepara o texto da transcrição
    full_text = "\n".join([f"{t['start']:.1f}: {t['text']}" for t in transcript])

    # Prompt para análise
    prompt = f"""Analise a transcrição abaixo e crie conteúdo editorial original que adiciona valor significativo:

1. 3-5 segmentos mais importantes (com timestamp início e fim)
2. Para cada segmento, crie um SCRIPT DE COMENTÁRIO original que:
   - Explica o contexto e importância
   - Adiciona insights exclusivos
   - Oferece análise crítica ou perspectiva única
   - Tem pelo menos 100 palavras para garantir substância
3. Um resumo geral transformativo
4. 3 sugestões de títulos otimizados
5. Uma descrição otimizada
6. Texto de introdução original (para o primeiro vídeo)
7. Texto de conclusão original (para o último vídeo)

Transcrição:
{full_text}

Responda em formato JSON:
{{
    "segments": [
        {{
            "start": float,
            "end": float,
            "motivo": "string",
            "script": "string - comentário editorial original de pelo menos 100 palavras"
        }},
        ...
    ],
    "summary": "string - análise transformativa original",
    "titles": ["string", ...],
    "description": "string - descrição com valor agregado",
    "intro_text": "string - introdução original para contextualizar",
    "outro_text": "string - conclusão original com insights finais"
}}"""

    # Faz a chamada à API
    content = make_api_call(prompt, temperature=0.7)

    # Processa a resposta
    try:
        # Tenta fazer parse do JSON
        try:
            result = json.loads(content)
        except json.JSONDecodeError:
            # Se falhar, tenta usar eval como fallback
            result = eval(content)

        return (
            result["segments"],
            result["summary"],
            result["titles"],
            result["description"],
            result.get("intro_text", ""),
            result.get("outro_text", "")
        )
    except Exception as e:
        raise Exception(f"Erro ao processar resposta do LLM: {str(e)}")