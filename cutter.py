import os
import ffmpeg

def cut_segments(video_path, segments, out_dir):
    """
    Corta o vídeo em segmentos usando ffmpeg.

    Args:
        video_path (str): Caminho do vídeo original
        segments (list): Lista de dicionários com start, end e motivo
        out_dir (str): Diretório para salvar os segmentos

    Returns:
        list: Lista de caminhos dos segmentos gerados
    """
    # Cria o diretório se não existir
    os.makedirs(out_dir, exist_ok=True)

    output_paths = []
    for i, segment in enumerate(segments):
        # Nome do arquivo de saída
        output_name = f"segment_{i+1:02d}.mp4"
        output_path = os.path.join(out_dir, output_name)

        try:
            # Corta o vídeo usando ffmpeg
            stream = ffmpeg.input(video_path)
            stream = ffmpeg.output(
                stream,
                output_path,
                ss=segment["start"],
                t=segment["end"] - segment["start"],
                acodec="copy",
                vcodec="copy"
            )
            ffmpeg.run(stream, overwrite_output=True, capture_stdout=True, capture_stderr=True)

            output_paths.append(output_path)

        except ffmpeg.Error as e:
            print(f"Erro ao cortar segmento {i+1}: {str(e)}")
            continue

    return output_paths