import os
import ffmpeg

def cut_segments(video_path, segments, out_dir):
    """
    Corta o vídeo em segmentos usando ffmpeg.

    Args:
        video_path (str): Caminho do vídeo original
        segments (list): Lista de dicionários com start, end e motivo
        out_dir (str): Diretório para salvar os segmentos

    Returns:
        list: Lista de caminhos dos segmentos gerados
    """
    # Cria o diretório se não existir
    os.makedirs(out_dir, exist_ok=True)

    # Verifica se é um arquivo de demonstração
    if video_path.endswith('_demo.txt'):
        print("⚠️ Modo demonstração - criando segmentos simulados...")
        return create_demo_segments(segments, out_dir)

    output_paths = []
    for i, segment in enumerate(segments):
        # Nome do arquivo de saída
        output_name = f"segment_{i+1:02d}.mp4"
        output_path = os.path.join(out_dir, output_name)

        try:
            # Corta o vídeo usando ffmpeg
            stream = ffmpeg.input(video_path)
            stream = ffmpeg.output(
                stream,
                output_path,
                ss=segment["start"],
                t=segment["end"] - segment["start"],
                acodec="copy",
                vcodec="copy"
            )
            ffmpeg.run(stream, overwrite_output=True, capture_stdout=True, capture_stderr=True)

            output_paths.append(output_path)

        except ffmpeg.Error as e:
            print(f"Erro ao cortar segmento {i+1}: {str(e)}")
            # Em caso de erro, cria segmento de demonstração
            demo_path = create_demo_segment(i+1, segment, out_dir)
            if demo_path:
                output_paths.append(demo_path)
            continue

    return output_paths

def create_demo_segments(segments, out_dir):
    """
    Cria segmentos de demonstração quando não há vídeo real.
    """
    output_paths = []

    for i, segment in enumerate(segments):
        demo_path = create_demo_segment(i+1, segment, out_dir)
        if demo_path:
            output_paths.append(demo_path)

    return output_paths

def create_demo_segment(segment_num, segment, out_dir):
    """
    Cria um arquivo de demonstração para um segmento específico.
    """
    output_name = f"segment_{segment_num:02d}_demo.txt"
    output_path = os.path.join(out_dir, output_name)

    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"""SEGMENTO {segment_num} - DEMONSTRAÇÃO

Tempo: {segment['start']:.1f}s - {segment['end']:.1f}s
Duração: {segment['end'] - segment['start']:.1f}s

Motivo da Seleção:
{segment['motivo']}

Script Editorial:
{segment.get('script', 'Script não disponível')}

---
Este é um arquivo de demonstração representando o segmento de vídeo cortado.
Em produção, este seria um arquivo .mp4 com o trecho específico do vídeo.
""")

        print(f"✅ Segmento demo {segment_num} criado: {output_path}")
        return output_path

    except Exception as e:
        print(f"❌ Erro ao criar segmento demo {segment_num}: {str(e)}")
        return None