import os
from moviepy.editor import (
    VideoFileClip, 
    AudioFileClip, 
    TextClip, 
    CompositeVideoClip, 
    CompositeAudioClip,
    concatenate_videoclips,
    ColorClip
)
from gtts import gTTS
import tempfile

def create_intro_outro_audio(text, lang="pt", filename_prefix="temp"):
    """
    Cria arquivo de áudio TTS para introdução ou conclusão.
    """
    tts = gTTS(text=text, lang=lang, slow=False)
    temp_file = f"{filename_prefix}_{hash(text) % 10000}.mp3"
    tts.save(temp_file)
    return temp_file

def add_text_overlay(clip, text, position="bottom", fontsize=24, color="white"):
    """
    Adiciona sobreposição de texto ao clipe.
    """
    # Quebra o texto em linhas para melhor legibilidade
    words = text.split()
    lines = []
    current_line = ""
    max_chars_per_line = 50
    
    for word in words:
        if len(current_line + word) < max_chars_per_line:
            current_line += word + " "
        else:
            lines.append(current_line.strip())
            current_line = word + " "
    
    if current_line:
        lines.append(current_line.strip())
    
    # Pega apenas as primeiras 3 linhas para não sobrecarregar
    display_text = "\n".join(lines[:3])
    if len(lines) > 3:
        display_text += "..."
    
    # Cria o texto com fundo semi-transparente
    txt_clip = TextClip(
        display_text,
        fontsize=fontsize,
        color=color,
        font='Arial-Bold',
        method='caption',
        size=(clip.w * 0.8, None)
    ).set_duration(clip.duration)
    
    # Posiciona o texto
    if position == "bottom":
        txt_clip = txt_clip.set_pos(('center', clip.h * 0.8))
    elif position == "top":
        txt_clip = txt_clip.set_pos(('center', clip.h * 0.1))
    else:
        txt_clip = txt_clip.set_pos(('center', 'center'))
    
    # Cria fundo semi-transparente para o texto
    bg_clip = ColorClip(
        size=(clip.w, txt_clip.h + 20),
        color=(0, 0, 0)
    ).set_opacity(0.7).set_duration(clip.duration)
    
    if position == "bottom":
        bg_clip = bg_clip.set_pos(('center', clip.h * 0.75))
    elif position == "top":
        bg_clip = bg_clip.set_pos(('center', clip.h * 0.05))
    else:
        bg_clip = bg_clip.set_pos(('center', 'center'))
    
    return CompositeVideoClip([clip, bg_clip, txt_clip])

def create_intro_clip(intro_text, duration=3):
    """
    Cria um clipe de introdução com texto.
    """
    # Cria um clipe colorido de fundo
    bg = ColorClip(size=(1280, 720), color=(0, 20, 40), duration=duration)
    
    # Adiciona texto da introdução
    title = TextClip(
        "ANÁLISE EXCLUSIVA",
        fontsize=40,
        color='white',
        font='Arial-Bold'
    ).set_duration(duration).set_pos(('center', 200))
    
    subtitle = TextClip(
        intro_text,
        fontsize=24,
        color='lightblue',
        font='Arial',
        method='caption',
        size=(1000, None)
    ).set_duration(duration).set_pos(('center', 350))
    
    return CompositeVideoClip([bg, title, subtitle])

def create_outro_clip(outro_text, duration=3):
    """
    Cria um clipe de conclusão com texto.
    """
    # Cria um clipe colorido de fundo
    bg = ColorClip(size=(1280, 720), color=(40, 20, 0), duration=duration)
    
    # Adiciona texto da conclusão
    title = TextClip(
        "CONCLUSÃO",
        fontsize=40,
        color='white',
        font='Arial-Bold'
    ).set_duration(duration).set_pos(('center', 200))
    
    subtitle = TextClip(
        outro_text,
        fontsize=24,
        color='orange',
        font='Arial',
        method='caption',
        size=(1000, None)
    ).set_duration(duration).set_pos(('center', 350))
    
    return CompositeVideoClip([bg, title, subtitle])

def enrich_single_clip(segment_path, comment_text, segment_index, total_segments, intro_text=None, outro_text=None):
    """
    Enriquece um único clipe com narração, legendas e elementos visuais.
    """
    try:
        # Carrega o clipe original
        clip = VideoFileClip(segment_path)
        
        # Gera áudio TTS para o comentário
        tts_file = None
        if comment_text:
            tts_file = create_intro_outro_audio(
                comment_text, 
                filename_prefix=f"segment_{segment_index}_tts"
            )
            narration = AudioFileClip(tts_file)
            
            # Ajusta a duração do clipe para coincidir com a narração
            if narration.duration > clip.duration:
                clip = clip.loop(duration=narration.duration)
            elif narration.duration < clip.duration:
                clip = clip.subclip(0, narration.duration)
            
            # Mistura o áudio original com a narração
            original_audio = clip.audio.volumex(0.3)  # Reduz volume original
            mixed_audio = CompositeAudioClip([original_audio, narration.volumex(0.7)])
            clip = clip.set_audio(mixed_audio)
        
        # Adiciona sobreposição de texto
        if comment_text:
            clip = add_text_overlay(clip, comment_text, position="bottom")
        
        # Adiciona identificação do segmento
        segment_info = f"Segmento {segment_index}/{total_segments}"
        segment_clip = TextClip(
            segment_info,
            fontsize=16,
            color='yellow',
            font='Arial-Bold'
        ).set_duration(3).set_pos(('right', 'top')).set_margin(10)
        
        clip = CompositeVideoClip([clip, segment_clip])
        
        clips_to_join = []
        
        # Adiciona introdução apenas no primeiro segmento
        if segment_index == 1 and intro_text:
            intro_audio_file = create_intro_outro_audio(intro_text, filename_prefix="intro")
            intro_clip = create_intro_clip(intro_text)
            intro_narration = AudioFileClip(intro_audio_file)
            intro_clip = intro_clip.set_audio(intro_narration)
            clips_to_join.append(intro_clip)
            
            # Limpa arquivo temporário
            os.remove(intro_audio_file)
        
        clips_to_join.append(clip)
        
        # Adiciona conclusão apenas no último segmento
        if segment_index == total_segments and outro_text:
            outro_audio_file = create_intro_outro_audio(outro_text, filename_prefix="outro")
            outro_clip = create_outro_clip(outro_text)
            outro_narration = AudioFileClip(outro_audio_file)
            outro_clip = outro_clip.set_audio(outro_narration)
            clips_to_join.append(outro_clip)
            
            # Limpa arquivo temporário
            os.remove(outro_audio_file)
        
        # Junta todos os clipes
        if len(clips_to_join) > 1:
            final_clip = concatenate_videoclips(clips_to_join, method="compose")
        else:
            final_clip = clips_to_join[0]
        
        # Define caminho de saída
        output_path = segment_path.replace("segments", "enhanced")
        output_dir = os.path.dirname(output_path)
        os.makedirs(output_dir, exist_ok=True)
        
        # Exporta o vídeo enriquecido
        final_clip.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        # Limpa recursos
        final_clip.close()
        clip.close()
        
        # Remove arquivo TTS temporário
        if tts_file and os.path.exists(tts_file):
            os.remove(tts_file)
        
        return output_path
        
    except Exception as e:
        print(f"Erro ao enriquecer clipe {segment_index}: {str(e)}")
        return None

def enrich_all_clips(segment_paths, segments_with_comments, intro_text=None, outro_text=None):
    """
    Enriquece todos os clipes com comentários e elementos visuais.
    """
    enhanced_paths = []
    total_segments = len(segment_paths)
    
    for i, (path, segment_data) in enumerate(zip(segment_paths, segments_with_comments)):
        comment = segment_data.get("script", segment_data.get("motivo", ""))
        
        enhanced_path = enrich_single_clip(
            path, 
            comment, 
            i + 1, 
            total_segments,
            intro_text if i == 0 else None,
            outro_text if i == total_segments - 1 else None
        )
        
        if enhanced_path:
            enhanced_paths.append(enhanced_path)
    
    return enhanced_paths 