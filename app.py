import os
import streamlit as st
from extractor import get_transcript, download_video
from router_client import analyze_transcript
from cutter import cut_segments
from enhancer import enrich_all_clips
from youtube_optimizer import (
    optimize_titles_with_expert_strategies,
    generate_viral_descriptions,
    analyze_emotional_triggers,
    generate_thumbnail_suggestions
)

# Configuração da página
st.set_page_config(
    page_title="Cut Scene - Otimizador de Vídeos",
    page_icon="✂️",
    layout="wide"
)

# Título e descrição
st.title("Cut Scene - Otimizador de Vídeos")
st.markdown("""
Este aplicativo cria conteúdo transformativo original a partir de vídeos do YouTube:
1. Extrai os momentos mais importantes com análise de IA
2. Adiciona comentários e insights exclusivos
3. Gera narração original com TTS
4. Cria vídeos enriquecidos com legendas e elementos visuais
5. Garante conformidade com políticas de monetização do YouTube

**🎯 Valor Agregado**: Cada vídeo recebe análise editorial original, narração própria e elementos visuais únicos.
""")

# Input da URL
url = st.text_input("Cole a URL do vídeo do YouTube aqui")

# Botão de processamento
if st.button("Analisar e Gerar Cortes"):
    if not url:
        st.error("Por favor, informe a URL do vídeo.")
    else:
        try:
            # Cria diretórios de saída
            os.makedirs("outputs/segments", exist_ok=True)
            os.makedirs("outputs/enhanced", exist_ok=True)
            
            # 1. Transcrição
            with st.spinner("🔍 Obtendo transcrição..."):
                transcript = get_transcript(url)
                st.success(f"✅ {len(transcript)} blocos de texto extraídos.")
            
            # 2. Análise LLM (agora com scripts de comentários)
            with st.spinner("🤖 Gerando análise editorial original..."):
                segments, summary, titles, description, intro_text, outro_text = analyze_transcript(transcript)
                st.success(f"✅ {len(segments)} segmentos analisados com scripts de comentário original.")
            
            # 2.5. Otimização Estratégica (NOVO)
            with st.spinner("🎯 Aplicando estratégias de creators virais..."):
                # Prepara texto da transcrição para análise
                transcript_text = "\n".join([f"{t['start']:.1f}: {t['text']}" for t in transcript])
                
                # Otimiza títulos com estratégias de experts
                viral_titles = optimize_titles_with_expert_strategies(transcript_text, summary, segments)
                
                # Analisa gatilhos emocionais
                emotional_analysis = analyze_emotional_triggers(segments)
                
                # Gera descrição viral
                viral_description = generate_viral_descriptions(summary, segments, viral_titles)
                
                # Gera sugestões de thumbnail
                thumbnail_suggestions = generate_thumbnail_suggestions(summary, emotional_analysis)
                
                st.success(f"✅ Otimização viral completa - {len(viral_titles)} títulos expert gerados.")
            
            # 3. Download vídeo
            with st.spinner("⬇️ Baixando vídeo original..."):
                video_path = download_video(url, "outputs/original.mp4")
                st.success("✅ Vídeo baixado com sucesso.")
            
            # 4. Cortes brutos
            with st.spinner("✂️ Gerando cortes brutos..."):
                cut_paths = cut_segments(video_path, segments, "outputs/segments")
                st.success(f"✅ {len(cut_paths)} cortes brutos gerados.")
            
            # 5. Enriquecimento Editorial (NOVO)
            with st.spinner("🖌️ Enriquecendo com narração e elementos visuais..."):
                enhanced_paths = enrich_all_clips(
                    cut_paths, 
                    segments, 
                    intro_text, 
                    outro_text
                )
                st.success(f"✅ {len(enhanced_paths)} vídeos enriquecidos criados.")
            
            # 6. Gerar report.md expandido
            with st.spinner("📄 Gerando relatório editorial..."):
                with open("outputs/report.md", "w", encoding="utf-8") as f:
                    f.write(f"# Análise Editorial - Conteúdo Transformativo\n\n")
                    f.write(f"## Declaração de Valor Agregado\n\n")
                    f.write("Este conteúdo foi criado com análise editorial original, narração própria e elementos visuais únicos, ")
                    f.write("garantindo conformidade com as políticas de monetização do YouTube através de transformação significativa.\n\n")
                    f.write(f"## Resumo Transformativo\n\n{summary}\n\n")
                    f.write(f"## Introdução Original\n\n{intro_text}\n\n")
                    f.write("## Segmentos com Scripts Editoriais\n\n")
                    for i, seg in enumerate(segments):
                        f.write(f"### Segmento {i+1}: {seg['start']:.1f}s até {seg['end']:.1f}s\n\n")
                        f.write(f"**Justificativa**: {seg['motivo']}\n\n")
                        f.write(f"**Script Editorial Original**:\n{seg.get('script', 'Script não disponível')}\n\n")
                        f.write("---\n\n")
                    f.write(f"## Conclusão Original\n\n{outro_text}\n\n")
                    f.write("## Títulos Básicos\n\n")
                    for t in titles:
                        f.write(f"- {t}\n")
                    f.write(f"\n## 🎯 TÍTULOS VIRAIS (Estratégias de Experts)\n\n")
                    for i, vt in enumerate(viral_titles, 1):
                        f.write(f"{i}. **{vt}**\n")
                    f.write(f"\n## Análise de Gatilhos Emocionais\n\n")
                    f.write(f"**Emoções Primárias**: {', '.join(emotional_analysis.get('emocoes_primarias', []))}\n")
                    f.write(f"**Gatilhos Psicológicos**: {', '.join(emotional_analysis.get('gatilhos_psicologicos', []))}\n")
                    f.write(f"**Elementos Virais**: {', '.join(emotional_analysis.get('elementos_virais', []))}\n")
                    f.write(f"**Estratégia Recomendada**: {emotional_analysis.get('estrategia_recomendada', '')}\n\n")
                    f.write(f"## Descrição Básica\n\n{description}\n\n")
                    f.write(f"## 🚀 DESCRIÇÃO VIRAL\n\n{viral_description}\n\n")
                    f.write(f"## 🖼️ Sugestões de Thumbnail\n\n{thumbnail_suggestions}\n\n")
                    f.write("## Conformidade com Políticas\n\n")
                    f.write("- ✅ Comentário editorial original para cada segmento\n")
                    f.write("- ✅ Narração própria gerada por TTS\n")
                    f.write("- ✅ Elementos visuais únicos (legendas, introdução, conclusão)\n")
                    f.write("- ✅ Transformação significativa do conteúdo original\n")
                    f.write("- ✅ Valor educacional e analítico agregado\n")
                st.success("✅ Relatório editorial completo gerado.")
            
            # Exibe resultados
            st.markdown("## 📊 Resultados do Conteúdo Transformativo")
            
            # Exibe declaração de conformidade
            st.success("✅ **Conformidade com Políticas de Monetização**: Conteúdo transformativo com valor agregado original")
            
            # Exibe resumo
            st.markdown("### Análise Transformativa")
            st.write(summary)
            
            # Exibe introdução e conclusão
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("### Introdução Original")
                st.write(intro_text)
            with col2:
                st.markdown("### Conclusão Original")
                st.write(outro_text)
            
            # Exibe títulos sugeridos  
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("### Títulos Básicos")
                for t in titles:
                    st.markdown(f"- {t}")
            
            with col2:
                st.markdown("### 🎯 TÍTULOS VIRAIS (Expert)")
                for i, vt in enumerate(viral_titles, 1):
                    st.markdown(f"**{i}.** {vt}")
            
            # Análise de gatilhos emocionais
            st.markdown("### 🧠 Análise Psicológica")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.markdown("**Emoções Primárias**")
                for emotion in emotional_analysis.get('emocoes_primarias', []):
                    st.markdown(f"• {emotion}")
            with col2:
                st.markdown("**Gatilhos Psicológicos**")
                for trigger in emotional_analysis.get('gatilhos_psicologicos', []):
                    st.markdown(f"• {trigger}")
            with col3:
                st.markdown("**Elementos Virais**")
                for element in emotional_analysis.get('elementos_virais', []):
                    st.markdown(f"• {element}")
            
            st.info(f"**Estratégia Recomendada**: {emotional_analysis.get('estrategia_recomendada', '')}")
            
            # Descrições
            st.markdown("### 📝 Descrições")
            tab1, tab2 = st.tabs(["Descrição Básica", "🚀 Descrição Viral"])
            with tab1:
                st.write(description)
            with tab2:
                st.write(viral_description)
            
            # Sugestões de thumbnail
            st.markdown("### 🖼️ Sugestões de Thumbnail")
            st.write(thumbnail_suggestions)
            
            # Exibe segmentos com scripts
            st.markdown("### Segmentos com Scripts Editoriais")
            for i, seg in enumerate(segments):
                with st.expander(f"Segmento {i+1}: {seg['start']:.1f}s - {seg['end']:.1f}s"):
                    st.write(f"**Justificativa**: {seg['motivo']}")
                    st.write(f"**Script Editorial**: {seg.get('script', 'Script não disponível')}")
            
            # Links para download
            st.markdown("### 📥 Downloads")
            st.markdown("- [Relatório Editorial Completo (report.md)](outputs/report.md)")
            
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("#### Cortes Brutos")
                for path in cut_paths:
                    filename = os.path.basename(path)
                    st.markdown(f"- [{filename}]({path})")
            
            with col2:
                st.markdown("#### Vídeos Enriquecidos (Finais)")
                for path in enhanced_paths:
                    filename = os.path.basename(path)
                    st.markdown(f"- [{filename}]({path})")
            
            # Informações sobre valor agregado
            st.markdown("### 🎯 Valor Agregado")
            st.info("""
            **Elementos de Transformação Original**:
            - Narração própria com TTS em português
            - Análise e comentários exclusivos para cada segmento
            - Elementos visuais únicos (legendas, introdução, conclusão)
            - Scripts editoriais originais com insights únicos
            - Estrutura narrativa própria com introdução e conclusão
            - **NOVO**: Títulos otimizados com estratégias de MrBeast, Paddy Galloway, Derral Eves e Ryan Trahan
            - **NOVO**: Análise psicológica de gatilhos emocionais
            - **NOVO**: Descrições com copywriting viral
            - **NOVO**: Sugestões de thumbnail baseadas em análise emocional
            """)
                
        except Exception as e:
            st.error(f"❌ Erro durante o processamento: {str(e)}")
            st.error("Por favor, verifique a URL e tente novamente.") 